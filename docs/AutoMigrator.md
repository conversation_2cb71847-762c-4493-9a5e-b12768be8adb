# AutoMigrator - Vue 2 到 Vue 3 全自动迁移工具

AutoMigrator 是一个全自动化的 Vue 2 到 Vue 3 迁移工具，它协调执行所有必要的迁移步骤，提供一站式的迁移解决方案。

## 功能特性

- 🔄 **全自动迁移流程**：一键执行完整的 Vue 2 到 Vue 3 迁移
- ⚙️ **配置文件复制**：自动复制优化的 vue.config.js 配置
- 📦 **依赖更新**：智能更新 package.json 中的依赖版本
- 🎨 **样式迁移**：自动迁移 Sass/SCSS 文件中的 Element UI 样式
- 🔄 **代码转换**：使用 gogocode 进行 Vue 代码自动转换
- 🔧 **构建修复**：自动检测和修复构建错误
- ✅ **页面验证**：运行时页面状态验证（仅在构建成功时执行）

## 迁移步骤

AutoMigrator 按以下顺序执行 6 个步骤：

1. **配置文件复制** - 复制 `vue.config.js` 到目标目录
2. **Package.json 迁移** - 更新依赖版本和配置
3. **Sass 迁移** - 转换 Element UI 到 Element Plus 的样式
4. **Vue 代码迁移** - 转换 Vue 2 代码到 Vue 3
5. **构建修复** - 自动检测和修复构建错误
6. **页面验证** - 验证页面运行状态（仅在构建成功时）

## 命令行使用

### 基本用法

```bash
# 执行完整迁移
npx auto-migrator migrate /path/to/vue2-project /path/to/vue3-project

# 预览模式（不实际修改文件）
npx auto-migrator migrate /path/to/vue2-project /path/to/vue3-project --dry-run

# 详细输出
npx auto-migrator migrate /path/to/vue2-project /path/to/vue3-project --verbose
```

### 高级选项

```bash
# 跳过特定步骤
npx auto-migrator migrate /path/to/vue2-project /path/to/vue3-project --skip-steps sass,validate

# 自定义构建命令
npx auto-migrator migrate /path/to/vue2-project /path/to/vue3-project \
  --build-command "pnpm build" \
  --dev-command "pnpm dev" \
  --install-command "pnpm install"

# 自定义构建修复参数
npx auto-migrator migrate /path/to/vue2-project /path/to/vue3-project \
  --max-build-attempts 10

# 自定义页面验证参数
npx auto-migrator migrate /path/to/vue2-project /path/to/vue3-project \
  --page-validation-port 8080 \
  --page-validation-timeout 60000
```

### 路径验证

```bash
# 验证迁移路径的有效性
npx auto-migrator validate-paths /path/to/vue2-project /path/to/vue3-project
```

## 编程接口

### 基本使用

```javascript
const AutoMigrator = require('galaxy-transit/src/app/AutoMigrator');

const migrator = new AutoMigrator(
  '/path/to/vue2-project',  // 源项目路径
  '/path/to/vue3-project',  // 目标项目路径
  {
    verbose: true,
    dryRun: false
  }
);

try {
  const result = await migrator.migrate();
  
  if (result.success) {
    console.log('迁移成功完成！');
  } else {
    console.log('迁移部分完成，请查看详细报告');
  }
} catch (error) {
  console.error('迁移失败:', error.message);
}
```

### 高级配置

```javascript
const migrator = new AutoMigrator(sourceProject, targetProject, {
  verbose: true,
  dryRun: false,
  skipSteps: ['validate'], // 跳过页面验证
  
  // 构建修复选项
  buildFixerOptions: {
    buildCommand: 'pnpm build',
    devCommand: 'pnpm dev',
    installCommand: 'pnpm install',
    maxAttempts: 10,
    mode: 'build'
  },
  
  // 页面验证选项
  pageValidatorOptions: {
    port: 8080,
    timeout: 60000,
    headless: true,
    autoFix: false
  }
});
```

## 配置选项

### 构造函数选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `verbose` | boolean | `false` | 是否显示详细日志 |
| `dryRun` | boolean | `false` | 预览模式，不实际修改文件 |
| `skipSteps` | string[] | `[]` | 要跳过的步骤列表 |
| `buildFixerOptions` | object | `{}` | 构建修复器选项 |
| `pageValidatorOptions` | object | `{}` | 页面验证器选项 |

### 可跳过的步骤

- `config` - 跳过配置文件复制
- `package` - 跳过 package.json 迁移
- `sass` - 跳过 Sass 迁移
- `vue` - 跳过 Vue 代码迁移
- `build` - 跳过构建修复
- `validate` - 跳过页面验证

## 返回结果

迁移完成后返回包含以下信息的结果对象：

```javascript
{
  startTime: 1234567890,
  endTime: 1234567895,
  success: true,
  errors: [],
  stepResults: {
    configCopy: { success: true, message: '...' },
    packageMigration: { success: true, message: '...' },
    sassMigration: { success: true, totalFiles: 5, processedFiles: 5 },
    vueMigration: { success: true, totalFiles: 10, processedFiles: 10 },
    buildFix: { success: true, message: '...' },
    pageValidation: { success: true, message: '...' }
  }
}
```

## 错误处理

- **配置文件复制失败**：会终止整个迁移流程
- **Package.json 迁移失败**：会终止整个迁移流程
- **Sass 迁移失败**：会终止整个迁移流程
- **Vue 代码迁移失败**：会终止整个迁移流程
- **构建修复失败**：不会终止流程，但会跳过页面验证
- **页面验证失败**：不会终止流程，仅记录错误

## 最佳实践

1. **备份原项目**：在迁移前务必备份原始项目
2. **使用预览模式**：先使用 `--dry-run` 预览迁移效果
3. **分步验证**：可以跳过某些步骤进行分步验证
4. **查看详细日志**：使用 `--verbose` 获取详细的迁移信息
5. **手动验证**：迁移完成后手动验证关键功能

## 故障排除

### 常见问题

1. **源项目路径不存在**
   - 检查路径是否正确
   - 确保有读取权限

2. **目标目录权限不足**
   - 检查目标目录的写入权限
   - 使用 sudo 或更改目录权限

3. **构建修复失败**
   - 查看详细错误日志
   - 手动修复特定的构建错误
   - 考虑跳过构建修复步骤

4. **页面验证超时**
   - 增加超时时间
   - 检查开发服务器是否正常启动
   - 考虑跳过页面验证步骤

### 获取帮助

```bash
# 查看帮助信息
npx auto-migrator --help

# 查看特定命令的帮助
npx auto-migrator migrate --help
```
