#!/usr/bin/env node

require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const AutoMigrator = require('../src/app/AutoMigrator');

const program = new Command();

program
  .name('auto-migrator')
  .description('Vue 2 到 Vue 3 全自动迁移工具')
  .version('1.0.0');

program
  .command('migrate <project-path> [target-path]')
  .description('🚀 执行完整的 Vue 2 到 Vue 3 自动迁移 (如果不指定target-path则就地迁移)')
  .option('--verbose', '显示详细信息')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--skip-steps <steps>', '跳过指定步骤，用逗号分隔 (config,package,sass,vue,build,validate)')
  .option('--build-command <cmd>', '自定义构建命令', 'npm run build')
  .option('--dev-command <cmd>', '自定义开发命令', 'npm run dev')
  .option('--install-command <cmd>', '自定义安装命令', 'npm install')
  .option('--max-build-attempts <num>', '最大构建修复尝试次数', '6')
  .option('--page-validation-port <port>', '页面验证端口', '9527')
  .option('--page-validation-timeout <ms>', '页面验证超时时间(毫秒)', '30000')
  .option('--username <username>', '页面验证登录用户名', 'admin')
  .option('--password <password>', '页面验证登录密码', '111111')
  .action(async (projectPath, targetPath, options) => {
    try {
      const resolvedProjectPath = path.resolve(projectPath);
      const resolvedTargetPath = targetPath ? path.resolve(targetPath) : null;

      // 验证项目路径存在
      if (!await fs.pathExists(resolvedProjectPath)) {
        throw new Error(`项目路径不存在: ${resolvedProjectPath}`);
      }

      // 解析跳过的步骤
      const skipSteps = options.skipSteps ? options.skipSteps.split(',').map(s => s.trim()) : [];

      console.log(chalk.blue('🚀 开始 Vue 2 到 Vue 3 全自动迁移...'));
      if (resolvedTargetPath) {
        console.log(chalk.gray(`   源项目: ${resolvedProjectPath}`));
        console.log(chalk.gray(`   目标项目: ${resolvedTargetPath}`));
      } else {
        console.log(chalk.gray(`   项目: ${resolvedProjectPath} (就地迁移)`));
      }
      
      if (options.dryRun) {
        console.log(chalk.yellow('   🔍 预览模式：不会实际修改文件'));
      }

      if (skipSteps.length > 0) {
        console.log(chalk.yellow(`   ⏭️  跳过步骤: ${skipSteps.join(', ')}`));
      }

      const migrator = new AutoMigrator(resolvedProjectPath, resolvedTargetPath, {
        verbose: options.verbose || false,
        dryRun: options.dryRun || false,
        skipSteps: skipSteps,
        buildFixerOptions: {
          buildCommand: options.buildCommand,
          devCommand: options.devCommand,
          installCommand: options.installCommand,
          maxAttempts: parseInt(options.maxBuildAttempts) || 6
        },
        pageValidatorOptions: {
          port: parseInt(options.pageValidationPort) || 3000,
          timeout: parseInt(options.pageValidationTimeout) || 30000,
          username: options.username || 'admin',
          password: options.password || '111111'
        }
      });

      const result = await migrator.migrate();

      if (result.success) {
        console.log(chalk.green('\n🎉 迁移成功完成！'));
        process.exit(0);
      } else {
        console.log(chalk.yellow('\n⚠️  迁移部分完成，请查看报告'));
        process.exit(1);
      }
    } catch (error) {
      console.error(chalk.red(`\n❌ 迁移失败: ${error.message}`));
      
      if (options.verbose) {
        console.error(chalk.gray('\n详细错误信息:'));
        console.error(chalk.gray(error.stack));
      }
      
      process.exit(1);
    }
  });

program
  .command('validate-paths <source-path> <target-path>')
  .description('🔍 验证迁移路径的有效性')
  .action(async (sourcePath, targetPath) => {
    try {
      const resolvedSourcePath = path.resolve(sourcePath);
      const resolvedTargetPath = path.resolve(targetPath);

      console.log(chalk.blue('🔍 验证迁移路径...'));

      // 检查源路径
      if (!await fs.pathExists(resolvedSourcePath)) {
        throw new Error(`源项目路径不存在: ${resolvedSourcePath}`);
      }

      const sourcePackageJson = path.join(resolvedSourcePath, 'package.json');
      if (!await fs.pathExists(sourcePackageJson)) {
        throw new Error(`源项目缺少 package.json: ${sourcePackageJson}`);
      }

      // 读取并验证 package.json
      const packageData = await fs.readJson(sourcePackageJson);
      const hasVue2 = packageData.dependencies?.vue?.startsWith('^2') || 
                      packageData.dependencies?.vue?.startsWith('~2') ||
                      packageData.dependencies?.vue?.startsWith('2');

      console.log(chalk.green(`✅ 源项目路径有效: ${resolvedSourcePath}`));
      console.log(chalk.gray(`   项目名称: ${packageData.name || '未知'}`));
      console.log(chalk.gray(`   Vue 版本: ${packageData.dependencies?.vue || '未找到'}`));
      
      if (hasVue2) {
        console.log(chalk.green('✅ 检测到 Vue 2 项目'));
      } else {
        console.log(chalk.yellow('⚠️  未检测到 Vue 2 依赖，请确认这是一个 Vue 2 项目'));
      }

      // 检查目标路径
      if (await fs.pathExists(resolvedTargetPath)) {
        const targetFiles = await fs.readdir(resolvedTargetPath);
        if (targetFiles.length > 0) {
          console.log(chalk.yellow(`⚠️  目标目录不为空: ${resolvedTargetPath}`));
          console.log(chalk.gray('   迁移时将覆盖现有文件'));
        } else {
          console.log(chalk.green(`✅ 目标目录为空: ${resolvedTargetPath}`));
        }
      } else {
        console.log(chalk.blue(`📁 目标目录将被创建: ${resolvedTargetPath}`));
      }

      console.log(chalk.green('\n✅ 路径验证完成，可以开始迁移'));
    } catch (error) {
      console.error(chalk.red(`❌ 路径验证失败: ${error.message}`));
      process.exit(1);
    }
  });

if (process.argv.length === 2) {
  program.help();
}

program.parse();
