const AutoMigrator = require('../src/app/AutoMigrator');
const path = require('path');
const fs = require('fs-extra');

describe('AutoMigrator DevServer Integration', () => {
  let testProjectPath;
  let autoMigrator;

  beforeEach(async () => {
    // 创建测试项目目录
    testProjectPath = path.join(__dirname, 'fixtures', 'test-project-devserver');
    await fs.ensureDir(testProjectPath);

    // 创建基本的 package.json
    const packageJson = {
      name: 'test-vue-project',
      version: '1.0.0',
      scripts: {
        dev: 'echo "Mock dev server starting..." && sleep 2',
        build: 'echo "Mock build completed"'
      },
      dependencies: {
        vue: '^2.6.14'
      }
    };
    await fs.writeJson(path.join(testProjectPath, 'package.json'), packageJson, { spaces: 2 });

    autoMigrator = new AutoMigrator(testProjectPath, null, {
      verbose: true,
      dryRun: true,
      skipSteps: ['config', 'package', 'sass', 'vue', 'build'] // 只测试 devserver 步骤
    });
  });

  afterEach(async () => {
    // 清理资源
    if (autoMigrator) {
      await autoMigrator.cleanup();
    }
    
    // 清理测试目录
    await fs.remove(testProjectPath);
  });

  describe('startDevServer', () => {
    it('应该在构建成功时启动开发服务器', async () => {
      const buildResult = { success: true };
      
      const result = await autoMigrator.startDevServer(buildResult);
      
      expect(result).toBeDefined();
      expect(result.success).toBeDefined();
      expect(result.message).toContain('开发服务器');
    });

    it('应该在构建失败时跳过开发服务器启动', async () => {
      const buildResult = { success: false };
      
      const result = await autoMigrator.startDevServer(buildResult);
      
      expect(result.success).toBe(false);
      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('构建未成功');
    });

    it('应该在跳过步骤时返回跳过状态', async () => {
      autoMigrator.options.skipSteps = ['devserver'];
      const buildResult = { success: true };
      
      const result = await autoMigrator.startDevServer(buildResult);
      
      expect(result.success).toBe(true);
      expect(result.skipped).toBe(true);
    });
  });

  describe('validatePages', () => {
    it('应该在开发服务器启动成功时执行页面验证', async () => {
      const devServerResult = { 
        success: true, 
        port: 3000, 
        baseUrl: 'http://localhost:3000' 
      };
      
      const result = await autoMigrator.validatePages(devServerResult);
      
      expect(result).toBeDefined();
      expect(result.success).toBeDefined();
    });

    it('应该在开发服务器启动失败时跳过页面验证', async () => {
      const devServerResult = { success: false };
      
      const result = await autoMigrator.validatePages(devServerResult);
      
      expect(result.success).toBe(false);
      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('开发服务器未启动成功');
    });
  });

  describe('migrate workflow', () => {
    it('应该包含开发服务器启动步骤', async () => {
      // 模拟所有步骤都成功
      autoMigrator.options.skipSteps = ['config', 'package', 'sass', 'vue', 'build', 'devserver', 'validate'];
      
      const result = await autoMigrator.migrate();
      
      expect(result.stepResults.devServerStart).toBeDefined();
      expect(result.stepResults.devServerStart.skipped).toBe(true);
    });

    it('应该正确传递开发服务器结果到页面验证', async () => {
      // 跳过前面的步骤，只测试流程
      autoMigrator.options.skipSteps = ['config', 'package', 'sass', 'vue', 'build', 'devserver', 'validate'];
      
      const result = await autoMigrator.migrate();
      
      // 验证步骤执行顺序
      expect(result.stepResults.buildFix).toBeDefined();
      expect(result.stepResults.devServerStart).toBeDefined();
      expect(result.stepResults.pageValidation).toBeDefined();
    });
  });

  describe('cleanup', () => {
    it('应该正确清理开发服务器资源', async () => {
      // 模拟有开发服务器实例
      autoMigrator.devServerManager = {
        stopDevServer: jest.fn().mockResolvedValue()
      };
      
      await autoMigrator.cleanup();
      
      expect(autoMigrator.devServerManager.stopDevServer).toHaveBeenCalled();
    });

    it('应该处理清理时的错误', async () => {
      // 模拟清理时出错
      autoMigrator.devServerManager = {
        stopDevServer: jest.fn().mockRejectedValue(new Error('清理失败'))
      };
      
      // 不应该抛出异常
      await expect(autoMigrator.cleanup()).resolves.not.toThrow();
    });
  });
});
